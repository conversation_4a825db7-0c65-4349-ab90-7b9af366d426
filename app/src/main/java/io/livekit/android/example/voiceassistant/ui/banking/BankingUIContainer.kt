package io.livekit.android.example.voiceassistant.ui.banking

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import io.livekit.android.example.voiceassistant.ui.banking.screens.AccountBalanceScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.AccountCreationScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.BiometricAuthScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.FundTransferScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.IdentityVerificationScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.LoanApplicationScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.PinEntryScreen
import io.livekit.android.example.voiceassistant.ui.banking.screens.TransactionHistoryScreen
import io.livekit.android.example.voiceassistant.data.ParsedFunctionResult
import io.livekit.android.example.voiceassistant.managers.toBankingUIState

/**
 * Main container for banking UI that displays different screens based on current state
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BankingUIContainer(
    uiManager: BankingUIManager,
    modifier: Modifier = Modifier
) {
    val state = uiManager.getCurrentState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Show error messages
    LaunchedEffect(state.errorMessage) {
        state.errorMessage?.let { message ->
            snackbarHostState.showSnackbar(message)
            uiManager.handleEvent(BankingUIEvent.ClearError)
        }
    }

    Box(modifier = modifier.fillMaxSize()) {
        // Main banking UI content
        AnimatedVisibility(
            visible = state.currentScreen != BankingScreen.None,
            enter = slideInHorizontally(
                initialOffsetX = { it },
                animationSpec = tween(300)
            ) + fadeIn(animationSpec = tween(300)),
            exit = slideOutHorizontally(
                targetOffsetX = { it },
                animationSpec = tween(300)
            ) + fadeOut(animationSpec = tween(300))
        ) {
            Dialog(
                onDismissRequest = {
                    uiManager.handleEvent(BankingUIEvent.NavigateBack)
                },
                properties = DialogProperties(
                    usePlatformDefaultWidth = false,
                    dismissOnBackPress = true,
                    dismissOnClickOutside = false
                )
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(modifier = Modifier.fillMaxSize()) {
                        // Top app bar
                        TopAppBar(
                            title = {
                                Text(
                                    text = getScreenTitle(state.currentScreen),
                                    fontWeight = FontWeight.SemiBold
                                )
                            },
                            navigationIcon = {
                                IconButton(
                                    onClick = {
                                        uiManager.handleEvent(BankingUIEvent.NavigateBack)
                                    }
                                ) {
                                    Icon(
                                        imageVector = if (state.currentScreen == BankingScreen.PinEntry || 
                                                         state.currentScreen == BankingScreen.BiometricAuth) {
                                            Icons.Default.Close
                                        } else {
                                            Icons.Default.ArrowBack
                                        },
                                        contentDescription = "Back"
                                    )
                                }
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = Color.Transparent,
                                titleContentColor = MaterialTheme.colorScheme.onSurface
                            )
                        )

                        // Screen content
                        AnimatedContent(
                            targetState = state.currentScreen,
                            transitionSpec = {
                                slideInHorizontally(
                                    initialOffsetX = { width -> width },
                                    animationSpec = tween(300)
                                ) togetherWith slideOutHorizontally(
                                    targetOffsetX = { width -> -width },
                                    animationSpec = tween(300)
                                )
                            },
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = 16.dp)
                        ) { screen ->
                            when (screen) {
                                BankingScreen.AccountCreation -> {
                                    AccountCreationScreen(
                                        state = state.accountCreation,
                                        onStateChange = { newState ->
                                            uiManager.handleEvent(BankingUIEvent.UpdateAccountCreation(newState))
                                        },
                                        onSubmit = {
                                            uiManager.handleEvent(BankingUIEvent.SubmitForm(BankingScreen.AccountCreation))
                                        }
                                    )
                                }

                                BankingScreen.FundTransfer -> {
                                    FundTransferScreen(
                                        state = state.fundTransfer,
                                        onStateChange = { newState ->
                                            uiManager.handleEvent(BankingUIEvent.UpdateFundTransfer(newState))
                                        },
                                        onSubmit = {
                                            uiManager.handleEvent(BankingUIEvent.SubmitForm(BankingScreen.FundTransfer))
                                        }
                                    )
                                }

                                BankingScreen.LoanApplication -> {
                                    LoanApplicationScreen(
                                        state = state.loanApplication,
                                        onStateChange = { newState ->
                                            uiManager.handleEvent(BankingUIEvent.UpdateLoanApplication(newState))
                                        },
                                        onSubmit = {
                                            uiManager.handleEvent(BankingUIEvent.SubmitForm(BankingScreen.LoanApplication))
                                        }
                                    )
                                }

                                BankingScreen.TransactionHistory -> {
                                    TransactionHistoryScreen(
                                        state = state.transactionHistory,
                                        onStateChange = { newState ->
                                            uiManager.handleEvent(BankingUIEvent.UpdateTransactionHistory(newState))
                                        }
                                    )
                                }

                                BankingScreen.IdentityVerification -> {
                                    IdentityVerificationScreen(
                                        state = state.identityVerification,
                                        onStateChange = { newState ->
                                            uiManager.handleEvent(BankingUIEvent.UpdateIdentityVerification(newState))
                                        }
                                    )
                                }

                                BankingScreen.AccountBalance -> {
                                    AccountBalanceScreen(
                                        state = state.accountBalance,
                                        onStateChange = { newState ->
                                            uiManager.handleEvent(BankingUIEvent.UpdateAccountBalance(newState))
                                        }
                                    )
                                }

                                BankingScreen.PinEntry -> {
                                    PinEntryScreen(
                                        onPinEntered = { pin ->
                                            // Validate PIN and complete authentication
                                            val isValid = validatePin(pin)
                                            uiManager.handleEvent(
                                                BankingUIEvent.CompleteAuthentication(
                                                    success = isValid,
                                                    method = AuthMethod.PIN
                                                )
                                            )
                                        },
                                        onBiometricRequested = {
                                            uiManager.handleEvent(BankingUIEvent.NavigateToScreen(BankingScreen.BiometricAuth))
                                        }
                                    )
                                }

                                BankingScreen.BiometricAuth -> {
                                    BiometricAuthScreen(
                                        onAuthResult = { success, method ->
                                            uiManager.handleEvent(
                                                BankingUIEvent.CompleteAuthentication(
                                                    success = success,
                                                    method = method
                                                )
                                            )
                                        },
                                        onFallbackToPin = {
                                            uiManager.handleEvent(BankingUIEvent.NavigateToScreen(BankingScreen.PinEntry))
                                        }
                                    )
                                }

                                else -> {
                                    // Empty content for None state
                                }
                            }
                        }
                    }
                }
            }
        }

        // Snackbar for error messages
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        ) { snackbarData ->
            Snackbar(
                snackbarData = snackbarData,
                containerColor = MaterialTheme.colorScheme.errorContainer,
                contentColor = MaterialTheme.colorScheme.onErrorContainer
            )
        }
    }
}

/**
 * Get the title for each banking screen
 */
private fun getScreenTitle(screen: BankingScreen): String {
    return when (screen) {
        BankingScreen.AccountCreation -> "Open New Account"
        BankingScreen.FundTransfer -> "Transfer Funds"
        BankingScreen.LoanApplication -> "Apply for Loan"
        BankingScreen.TransactionHistory -> "Transaction History"
        BankingScreen.IdentityVerification -> "Identity Verification"
        BankingScreen.AccountBalance -> "Account Balance"
        BankingScreen.PinEntry -> "Enter PIN"
        BankingScreen.BiometricAuth -> "Biometric Authentication"
        BankingScreen.None -> ""
    }
}

/**
 * Dynamic Banking UI Container for server-triggered screens
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BankingUIContainer(
    uiManager: BankingUIManager,
    isVisible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    initialScreen: BankingScreen = BankingScreen.None,
    serverData: Any? = null
) {
    val state = uiManager.getCurrentState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Update UI state with server data when available
    LaunchedEffect(serverData) {
        if (serverData is ParsedFunctionResult) {
            val updatedState = serverData.toBankingUIState(state)
            // Update the UI manager with the new state
            when (serverData) {
                is ParsedFunctionResult.TransactionHistory -> {
                    uiManager.handleEvent(BankingUIEvent.UpdateTransactionHistory(updatedState.transactionHistory))
                }
                is ParsedFunctionResult.AccountBalance -> {
                    uiManager.handleEvent(BankingUIEvent.UpdateAccountBalance(updatedState.accountBalance))
                }
                is ParsedFunctionResult.FundTransfer -> {
                    uiManager.handleEvent(BankingUIEvent.UpdateFundTransfer(updatedState.fundTransfer))
                }
                is ParsedFunctionResult.LoanApplication -> {
                    uiManager.handleEvent(BankingUIEvent.UpdateLoanApplication(updatedState.loanApplication))
                }
                else -> { /* Handle other types as needed */ }
            }
        }
    }

    // Navigate to initial screen when specified
    LaunchedEffect(initialScreen) {
        if (initialScreen != BankingScreen.None && initialScreen != state.currentScreen) {
            uiManager.handleEvent(BankingUIEvent.NavigateToScreen(initialScreen))
        }
    }

    // Show error messages
    LaunchedEffect(state.errorMessage) {
        state.errorMessage?.let { message ->
            snackbarHostState.showSnackbar(message)
            uiManager.handleEvent(BankingUIEvent.ClearError)
        }
    }

    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = true,
                dismissOnClickOutside = false
            )
        ) {
            Card(
                modifier = modifier
                    .fillMaxSize()
                    .padding(8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(modifier = Modifier.fillMaxSize()) {
                    // Top app bar
                    TopAppBar(
                        title = {
                            Text(
                                text = getScreenTitle(state.currentScreen),
                                fontWeight = FontWeight.SemiBold
                            )
                        },
                        navigationIcon = {
                            IconButton(onClick = onDismiss) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "Close"
                                )
                            }
                        },
                        colors = TopAppBarDefaults.topAppBarColors(
                            containerColor = Color.Transparent,
                            titleContentColor = MaterialTheme.colorScheme.onSurface
                        )
                    )

                    // Screen content
                    AnimatedContent(
                        targetState = state.currentScreen,
                        transitionSpec = {
                            slideInHorizontally(
                                initialOffsetX = { width -> width },
                                animationSpec = tween(300)
                            ) togetherWith slideOutHorizontally(
                                targetOffsetX = { width -> -width },
                                animationSpec = tween(300)
                            )
                        },
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 16.dp)
                    ) { screen ->
                        when (screen) {
                            BankingScreen.TransactionHistory -> {
                                TransactionHistoryScreen(
                                    state = state.transactionHistory,
                                    onStateChange = { newState ->
                                        uiManager.handleEvent(BankingUIEvent.UpdateTransactionHistory(newState))
                                    }
                                )
                            }

                            BankingScreen.AccountBalance -> {
                                AccountBalanceScreen(
                                    state = state.accountBalance,
                                    onStateChange = { newState ->
                                        uiManager.handleEvent(BankingUIEvent.UpdateAccountBalance(newState))
                                    }
                                )
                            }

                            BankingScreen.FundTransfer -> {
                                FundTransferScreen(
                                    state = state.fundTransfer,
                                    onStateChange = { newState ->
                                        uiManager.handleEvent(BankingUIEvent.UpdateFundTransfer(newState))
                                    },
                                    onSubmit = {
                                        uiManager.handleEvent(BankingUIEvent.SubmitForm(BankingScreen.FundTransfer))
                                    }
                                )
                            }

                            BankingScreen.LoanApplication -> {
                                LoanApplicationScreen(
                                    state = state.loanApplication,
                                    onStateChange = { newState ->
                                        uiManager.handleEvent(BankingUIEvent.UpdateLoanApplication(newState))
                                    },
                                    onSubmit = {
                                        uiManager.handleEvent(BankingUIEvent.SubmitForm(BankingScreen.LoanApplication))
                                    }
                                )
                            }

                            BankingScreen.BiometricAuth -> {
                                BiometricAuthScreen(
                                    onAuthResult = { success, method ->
                                        uiManager.handleEvent(
                                            BankingUIEvent.CompleteAuthentication(
                                                success = success,
                                                method = method
                                            )
                                        )
                                    },
                                    onFallbackToPin = {
                                        uiManager.handleEvent(BankingUIEvent.NavigateToScreen(BankingScreen.PinEntry))
                                    }
                                )
                            }

                            else -> {
                                // Show a default message for unsupported screens
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "Screen content will appear here",
                                        style = MaterialTheme.typography.bodyLarge
                                    )
                                }
                            }
                        }
                    }
                }

                // Snackbar for error messages
                SnackbarHost(
                    hostState = snackbarHostState,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                ) { snackbarData ->
                    Snackbar(
                        snackbarData = snackbarData,
                        containerColor = MaterialTheme.colorScheme.errorContainer,
                        contentColor = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

/**
 * Simple PIN validation (in real app, this would be more secure)
 */
private fun validatePin(pin: String): Boolean {
    // For demo purposes, accept any 4-digit PIN
    return pin.length == 4 && pin.all { it.isDigit() }
}
