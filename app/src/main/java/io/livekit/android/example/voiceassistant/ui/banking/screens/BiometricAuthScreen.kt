package io.livekit.android.example.voiceassistant.ui.banking.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Fingerprint
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.ui.banking.AuthMethod
import io.livekit.android.example.voiceassistant.managers.RpcManager
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.delay

@Composable
fun BiometricAuthScreen(
    onAuthResult: (Boolean, AuthMethod) -> Unit,
    onFallbackToPin: () -> Unit,
    modifier: Modifier = Modifier,
    rpcManager: RpcManager? = null,
    serverParticipant: Participant.Identity? = null
) {
    var isAuthenticating by remember { mutableStateOf(false) }
    var authResult by remember { mutableStateOf<Boolean?>(null) }
    var authMessage by remember { mutableStateOf("") }

    // Handle biometric authentication with RPC call
    LaunchedEffect(isAuthenticating) {
        if (isAuthenticating) {
            try {
                BankAssistLogger.i("Starting biometric authentication process")
                delay(1500) // Simulate biometric scan time

                // Simulate successful biometric authentication
                val biometricSuccess = true // In real app, this would be actual biometric result

                if (biometricSuccess && rpcManager != null && serverParticipant != null) {
                    BankAssistLogger.i("Biometric authentication successful, sending RPC to server")
                    authMessage = "Sending authentication to server..."

                    // Send authentication RPC with user credentials
                    val authResponse = rpcManager.callAuthenticate(
                        participantIdentity = serverParticipant,
                        userId = "12345",
                        phoneLast4 = "7890"
                    )

                    if (authResponse != null && authResponse.success) {
                        BankAssistLogger.i("Server authentication successful: ${authResponse.message}")
                        authResult = true
                        authMessage = "Authentication successful!"
                        delay(1000)
                        onAuthResult(true, AuthMethod.FINGERPRINT)
                    } else {
                        BankAssistLogger.w("Server authentication failed: ${authResponse?.message ?: "No response"}")
                        authResult = false
                        authMessage = "Server authentication failed"
                        delay(2000)
                        onAuthResult(false, AuthMethod.FINGERPRINT)
                    }
                } else if (biometricSuccess) {
                    // Fallback when no RPC manager (for testing)
                    BankAssistLogger.i("Biometric authentication successful (no RPC)")
                    authResult = true
                    authMessage = "Authentication successful!"
                    delay(1000)
                    onAuthResult(true, AuthMethod.FINGERPRINT)
                } else {
                    BankAssistLogger.w("Biometric authentication failed")
                    authResult = false
                    authMessage = "Biometric authentication failed"
                    delay(2000)
                    onAuthResult(false, AuthMethod.FINGERPRINT)
                }
            } catch (e: Exception) {
                BankAssistLogger.e("Authentication error", e)
                authResult = false
                authMessage = "Authentication error: ${e.message}"
                delay(2000)
                onAuthResult(false, AuthMethod.FINGERPRINT)
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Fingerprint,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "Biometric Authentication",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = if (isAuthenticating) {
                        "Authenticating with server..."
                    } else {
                        "Place your finger on the sensor or look at the camera"
                    },
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    textAlign = TextAlign.Center
                )
            }
        }

        when {
            isAuthenticating -> {
                CircularProgressIndicator(modifier = Modifier.padding(32.dp))
                Text(
                    text = authMessage.ifEmpty { "Authenticating..." },
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
            }
            authResult == true -> {
                Text(
                    "✅ Authentication Successful",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(32.dp),
                    textAlign = TextAlign.Center
                )
                if (authMessage.isNotEmpty()) {
                    Text(
                        text = authMessage,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )
                }
            }
            authResult == false -> {
                Text(
                    "❌ Authentication Failed",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(32.dp),
                    textAlign = TextAlign.Center
                )
                if (authMessage.isNotEmpty()) {
                    Text(
                        text = authMessage,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )
                }
            }
            else -> {
                Button(
                    onClick = { isAuthenticating = true },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 32.dp)
                ) {
                    Text("Start Biometric Authentication")
                }

                OutlinedButton(
                    onClick = onFallbackToPin,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                    Text("Use PIN Instead")
                }
            }
        }
    }
}
