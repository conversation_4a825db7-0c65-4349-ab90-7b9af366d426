package io.livekit.android.example.voiceassistant.managers

import androidx.compose.runtime.*
import io.livekit.android.example.voiceassistant.data.*
import io.livekit.android.example.voiceassistant.ui.banking.*
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import kotlinx.coroutines.delay

/**
 * Manages dynamic screen display based on server function results
 */
@Stable
class DynamicScreenManager {
    
    private val _currentScreen = mutableStateOf<BankingScreen>(BankingScreen.None)
    val currentScreen: State<BankingScreen> = _currentScreen
    
    private val _screenData = mutableStateOf<Any?>(null)
    val screenData: State<Any?> = _screenData
    
    private val _isVisible = mutableStateOf(false)
    val isVisible: State<Boolean> = _isVisible
    
    private val _autoHideDelay = mutableStateOf(10000L) // 10 seconds default
    val autoHideDelay: State<Long> = _autoHideDelay
    
    /**
     * Show a banking screen with data from server function result
     */
    fun showScreenForFunctionResult(result: ParsedFunctionResult) {
        BankAssistLogger.i("Showing screen for function result: ${result::class.simpleName}")
        
        when (result) {
            is ParsedFunctionResult.TransactionHistory -> {
                _currentScreen.value = BankingScreen.TransactionHistory
                _screenData.value = result
                _isVisible.value = true
                BankAssistLogger.i("Displaying Transaction History screen with ${result.transactions.size} transactions")
            }
            
            is ParsedFunctionResult.AccountBalance -> {
                _currentScreen.value = BankingScreen.AccountBalance
                _screenData.value = result
                _isVisible.value = true
                BankAssistLogger.i("Displaying Account Balance screen with ${result.balances.size} accounts")
            }
            
            is ParsedFunctionResult.FundTransfer -> {
                _currentScreen.value = BankingScreen.FundTransfer
                _screenData.value = result
                _isVisible.value = true
                BankAssistLogger.i("Displaying Fund Transfer screen for transfer ${result.transferId}")
            }
            
            is ParsedFunctionResult.LoanApplication -> {
                _currentScreen.value = BankingScreen.LoanApplication
                _screenData.value = result
                _isVisible.value = true
                BankAssistLogger.i("Displaying Loan Application screen for application ${result.applicationId}")
            }
            
            is ParsedFunctionResult.Authentication -> {
                if (result.success) {
                    _currentScreen.value = BankingScreen.BiometricAuth
                    _screenData.value = result
                    _isVisible.value = true
                    BankAssistLogger.i("Displaying Authentication success screen")
                } else {
                    BankAssistLogger.w("Authentication failed, not showing screen")
                }
            }
            
            is ParsedFunctionResult.Error -> {
                BankAssistLogger.e("Function result error: ${result.message}")
                // Could show an error screen or toast
            }
            
            is ParsedFunctionResult.Unknown -> {
                BankAssistLogger.w("Unknown function result for ${result.functionName}")
                // Could show a generic info screen
            }
        }
    }
    
    /**
     * Hide the current screen
     */
    fun hideScreen() {
        BankAssistLogger.i("Hiding dynamic screen: ${_currentScreen.value}")
        _isVisible.value = false
        _currentScreen.value = BankingScreen.None
        _screenData.value = null
    }
    
    /**
     * Set auto-hide delay in milliseconds
     */
    fun setAutoHideDelay(delayMs: Long) {
        _autoHideDelay.value = delayMs
        BankAssistLogger.d("Auto-hide delay set to ${delayMs}ms")
    }
    
    /**
     * Check if a screen is currently visible
     */
    fun isScreenVisible(): Boolean = _isVisible.value
    
    /**
     * Get the current screen type
     */
    fun getCurrentScreenType(): BankingScreen = _currentScreen.value
    
    /**
     * Get typed screen data
     */
    fun <T> getScreenData(): T? {
        return screenData.value as? T
    }
}

/**
 * Composable function to create and manage DynamicScreenManager
 */
@Composable
fun rememberDynamicScreenManager(): DynamicScreenManager {
    return remember { DynamicScreenManager() }
}

/**
 * Auto-hide effect for dynamic screens
 */
@Composable
fun DynamicScreenAutoHideEffect(
    screenManager: DynamicScreenManager,
    onNextFunctionResult: () -> Unit = {}
) {
    val isVisible by screenManager.isVisible
    val autoHideDelay by screenManager.autoHideDelay
    val currentScreen by screenManager.currentScreen
    
    LaunchedEffect(isVisible, currentScreen) {
        if (isVisible && currentScreen != BankingScreen.None) {
            BankAssistLogger.d("Starting auto-hide timer for ${currentScreen} (${autoHideDelay}ms)")
            delay(autoHideDelay)
            
            if (screenManager.isScreenVisible()) {
                BankAssistLogger.i("Auto-hiding screen ${currentScreen} after ${autoHideDelay}ms")
                screenManager.hideScreen()
                onNextFunctionResult()
            }
        }
    }
}

/**
 * Convert ParsedFunctionResult to appropriate BankingUIState updates
 */
fun ParsedFunctionResult.toBankingUIState(currentState: BankingUIState): BankingUIState {
    return when (this) {
        is ParsedFunctionResult.TransactionHistory -> {
            currentState.copy(
                transactionHistory = currentState.transactionHistory.copy(
                    transactions = this.transactions.map { transactionData ->
                        Transaction(
                            id = transactionData.id,
                            type = transactionData.type,
                            amount = transactionData.amount,
                            currency = transactionData.currency,
                            description = transactionData.description,
                            timestamp = transactionData.timestamp,
                            balance = transactionData.balance ?: 0.0
                        )
                    },
                    isLoading = false
                )
            )
        }
        
        is ParsedFunctionResult.AccountBalance -> {
            currentState.copy(
                accountBalance = currentState.accountBalance.copy(
                    balances = this.balances.map { balanceData ->
                        AccountBalanceResponse(
                            accountId = balanceData.accountId,
                            balance = balanceData.balance,
                            currency = balanceData.currency,
                            accountType = balanceData.accountType,
                            lastUpdated = balanceData.lastUpdated
                        )
                    },
                    isLoading = false
                )
            )
        }
        
        is ParsedFunctionResult.FundTransfer -> {
            currentState.copy(
                fundTransfer = currentState.fundTransfer.copy(
                    amount = this.amount,
                    fromAccount = this.fromAccount,
                    toAccount = this.toAccount,
                    estimatedFee = this.fee ?: 0.0,
                    isSubmitting = false
                )
            )
        }

        is ParsedFunctionResult.LoanApplication -> {
            currentState.copy(
                loanApplication = currentState.loanApplication.copy(
                    requestedAmount = this.amount,
                    termMonths = this.term ?: 12,
                    isSubmitting = false
                )
            )
        }

        is ParsedFunctionResult.Authentication -> {
            currentState.copy(
                authState = currentState.authState.copy(
                    isAuthenticated = this.success,
                    authMethod = if (this.success) AuthMethod.VOICE else null,
                    sessionExpiry = this.expiresAt
                )
            )
        }
        
        else -> currentState
    }
}
