@file:OptIn(Beta::class)

package io.livekit.android.example.voiceassistant

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assistant
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import io.livekit.android.LiveKit
import io.livekit.android.annotations.Beta
import io.livekit.android.compose.local.RoomScope
import io.livekit.android.compose.state.rememberVoiceAssistant
import io.livekit.android.example.voiceassistant.managers.rememberDataStreamManager
import io.livekit.android.example.voiceassistant.managers.rememberEventManager
import io.livekit.android.example.voiceassistant.managers.rememberRpcManager
import io.livekit.android.example.voiceassistant.managers.rememberWakeWordManager
import io.livekit.android.example.voiceassistant.managers.rememberDynamicScreenManager
import io.livekit.android.example.voiceassistant.managers.DynamicScreenAutoHideEffect
import io.livekit.android.example.voiceassistant.ui.VoiceAssistantOverlay
import io.livekit.android.example.voiceassistant.ui.banking.rememberBankingUIManager
import io.livekit.android.example.voiceassistant.ui.banking.BankingUIContainer
import io.livekit.android.example.voiceassistant.ui.theme.LiveKitVoiceAssistantExampleTheme
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.util.LoggingLevel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LiveKit.loggingLevel = LoggingLevel.DEBUG
        requireNeededPermissions {
            requireToken { url, token ->
                setContent {
                    LiveKitVoiceAssistantExampleTheme(dynamicColor = false) {
                        Surface {
                            VoiceAssistant(
                                url,
                                token,
                                modifier = Modifier
                                    .fillMaxSize()
                            )
                        }
                    }
                }
            }
        }
    }

    @Composable
    fun VoiceAssistant(url: String, token: String, modifier: Modifier = Modifier) {
        RoomScope(
            url,
            token,
            audio = true,
            connect = true,
        ) { room ->
            val context = LocalContext.current
            val coroutineScope = rememberCoroutineScope()

            // Voice assistant overlay state
            var isVoiceOverlayVisible by remember { mutableStateOf(false) }
            var isVoiceListening by remember { mutableStateOf(false) }

            // Initialize dynamic screen manager
            val dynamicScreenManager = rememberDynamicScreenManager()

            // Initialize required managers
            val voiceAssistant = rememberVoiceAssistant()
            val rpcManager = rememberRpcManager(room)
            val dataStreamManager = rememberDataStreamManager(room)
            val eventManager = rememberEventManager(room) { functionResult ->
                // Show appropriate banking screen when function result is received
                dynamicScreenManager.showScreenForFunctionResult(functionResult)
            }
            val bankingUIManager = rememberBankingUIManager(rpcManager)

            // Demo: Set up data received event handling
            LaunchedEffect(room) {
                // This demonstrates how you would handle data received events
                // In a real implementation, this would be handled by LiveKit's event system

                // Example: Simulate receiving server data (commented out for now)
                // val sampleServerData = """{"type": "balance_update", "amount": 1500.00, "currency": "USD"}"""
                // eventManager.handleDataReceived(
                //     participant = someRemoteParticipant,
                //     data = sampleServerData.toByteArray()
                // )

                BankAssistLogger.i("Data received event handlers are ready")
            }

            // Wake word manager
            val wakeWordManager = rememberWakeWordManager(
                context = context,
                onWakeWordDetected = {
                    BankAssistLogger.logWakeWord("Wake word detected! Showing voice assistant overlay")
                    isVoiceOverlayVisible = true
                    isVoiceListening = true
                },
                onError = { error ->
                    BankAssistLogger.e("Wake word detection error: $error")
                }
            )

            val agentState = voiceAssistant.state
            LaunchedEffect(key1 = agentState) {
                BankAssistLogger.i("Agent state changed: $agentState")
            }

            // Auto-hide effect for dynamic screens
            DynamicScreenAutoHideEffect(
                screenManager = dynamicScreenManager,
                onNextFunctionResult = {
                    // Could trigger next action or return to main screen
                    BankAssistLogger.i("Dynamic screen auto-hidden, ready for next function result")
                }
            )

            // Start wake word detection when the room is connected
            LaunchedEffect(room) {
                try {
                    wakeWordManager.startListening()
                    BankAssistLogger.logWakeWord("Wake word detection started")
                } catch (e: Exception) {
                    BankAssistLogger.e("Failed to start wake word detection", e)
                }
            }

            Box(
                modifier = modifier
                    .fillMaxSize()
                    .windowInsetsPadding(WindowInsets.navigationBars.union(WindowInsets.ime))
            ) {
                // Simple background - all functionality moved to voice overlay
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Tap the assistant button or say 'picovoice' to start",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Floating Action Button for voice assistant
                FloatingActionButton(
                    onClick = {
                        BankAssistLogger.logVoiceOverlay("FAB clicked! Toggling voice assistant overlay")
                        isVoiceOverlayVisible = !isVoiceOverlayVisible
                        if (isVoiceOverlayVisible) {
                            isVoiceListening = true
                        }
                    },
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(16.dp),
                    containerColor = MaterialTheme.colorScheme.primary,
                    shape = CircleShape
                ) {
                    Icon(
                        imageVector = Icons.Default.Assistant,
                        contentDescription = "Voice Assistant",
                        tint = Color.White
                    )
                }

                // Voice Assistant Overlay
                VoiceAssistantOverlay(
                    isVisible = isVoiceOverlayVisible,
                    isListening = isVoiceListening,
                    onDismiss = {
                        BankAssistLogger.logVoiceOverlay("Voice overlay dismissed")
                        isVoiceOverlayVisible = false
                        isVoiceListening = false
                    },
                    onToggleListening = {
                        isVoiceListening = !isVoiceListening
                        BankAssistLogger.logVoiceOverlay("Voice listening toggled: $isVoiceListening")
                    },
                    bankingUIManager = bankingUIManager,
                    voiceAssistant = voiceAssistant, // Pass voice assistant for visualization
                    room = room // Pass room for transcriptions
                )

                // Dynamic Banking Screen Overlay (shows over everything when function results arrive)
                val isDynamicScreenVisible by dynamicScreenManager.isVisible
                val currentDynamicScreen by dynamicScreenManager.currentScreen

                if (isDynamicScreenVisible && currentDynamicScreen != io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.None) {
                    BankingUIContainer(
                        uiManager = bankingUIManager,
                        isVisible = true,
                        onDismiss = {
                            BankAssistLogger.i("Dynamic banking screen dismissed by user")
                            dynamicScreenManager.hideScreen()
                        },
                        modifier = Modifier.fillMaxSize(),
                        initialScreen = currentDynamicScreen,
                        serverData = dynamicScreenManager.screenData.value
                    )
                }
            } // End Box
        }
    }
}
